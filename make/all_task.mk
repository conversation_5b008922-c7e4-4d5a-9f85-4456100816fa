#
# Input variables
#
#   BUNDLED_TA_TASKS  - bundled ta tasks, will be added to tee.bin
#   BUNDLED_TA_TEST_TASKS  - bundled test ta tasks, will be added to tee.bin
#   DYNAMIC_TA_TASKS  - dynamic ta tasks, should be loaded by rcteeproxy
#   DYNAMIC_TA_TEST_TASKS  - dynamic test ta tasks, should be loaded by rcteeproxy

$(info Parsing RCTEE userspace tasks)
TASK_TOP_DIR := $(GET_LOCAL_DIR)

# define default vars
EXPORTED_OPTFLAGS :=
EXPORTED_COMPILEFLAGS :=
EXPORTED_CFLAGS :=
EXPORTED_CPPFLAGS :=
EXPORTED_INCLUDES :=
EXPORTED_ASMFLAGS :=
TOTAL_LIBS :=
TOTAL_USER_TARGETS :=

RCTEE_USER_OUTDIR := $(BUILDDIR)/user_product
RCTEE_SDK_DIR := $(BUILDDIR)/sdk
RCTEE_SDK_SYSROOT := $(RCTEE_SDK_DIR)/sysroot/
RCTEE_SDK_INCLUDE_DIR := $(RCTEE_SDK_SYSROOT)/usr/include
RCTEE_SDK_LIB_DIR := $(RCTEE_SDK_SYSROOT)/usr/lib
RCTEE_SDK_LICENSE_DIR := $(RCTEE_SDK_DIR)/licenses
RCTEE_SDK_LICENSE := $(RCTEE_SDK_DIR)/LICENSE
RCTEE_HOST_LIBRARY_BUILDDIR := $(BUILDDIR)/host_lib
TA_PACKAGE_TOOL := $(BUILDDIR)/host_tools/apploader_package_tool

#add host package first,TODO: should remove this
include build/tools/package_tool/rules.mk

#add some common configs for in-tree build
EXPORTED_INCLUDES += \
	-Ikernel/lk/include/shared \
	-Ikernel/rctee/include/uapi \
	-Ikernel/lk/include/uapi \
	-Ikernel/rctee/include/shared \
	-Iuser/base/include/user \
	-Ikernel/lk/arch/$(RCTEE_PLATFORM_ARCH)/include \

EXPORTED_ASMFLAGS += -DASSEMBLY
EXPORTED_COMPILEFLAGS += -DIMX8MP_LCDIF_INDEX=1 -ffunction-sections -fdata-sections
EXPORTED_CPPFLAGS += --std=c++17 -fno-exceptions -fno-rtti -fno-threadsafe-statics
EXPORTED_CFLAGS += --std=c17 -Wstrict-prototypes -Wwrite-strings

# Save userspace-global variables so we can restore kernel state
# TODO: remove this??
RCTEE_KERNEL_SAVED_ARCH := $(ARCH)
RCTEE_KERNEL_SAVED_ALLOW_FP_USE := $(ALLOW_FP_USE)
RCTEE_KERNEL_SAVED_SCS_ENABLED := $(SCS_ENABLED)

# while compiling user space we allow FP support
ALLOW_FP_USE := true

# tell the arch-specific makefiles to set flags required for SCS if supported
SCS_ENABLED := $(call TOBOOL,$(USER_SCS_ENABLED))

# Building trusty userspace
TRUSTY_USERSPACE := true

# Used by LTO, could be combined with TRUSTY_USERSPACE after this lands
USER_TASK_MODULE := true

ARCH := $(RCTEE_PLATFORM_ARCH)

# set user space clang configs
include make/clang.mk

ALL_TA_TASKS := $(BUNDLED_TA_TASKS) $(BUNDLED_TA_TEST_TASKS)  \
		  $(DYNAMIC_TA_TASKS) $(DYNAMIC_TA_TEST_TASKS)

# sort and remove duplicates for TA Tasks
ALL_TA_TASKS := $(sort $(ALL_TA_TASKS))

$(info ALL_TA_TASKS: $(ALL_TA_TASKS))

#
# define inculde-ta
#
define inculde-ta
$(eval COMP_PATH := $(1))\
$(eval include $(1)/rules.mk)
endef

#inculde all TAs
$(foreach ta,$(ALL_TA_TASKS),\
	$(call inculde-ta,$(ta)))

# remove duplicated elements
EXPORTED_OPTFLAGS := $(sort $(EXPORTED_OPTFLAGS))
EXPORTED_COMPILEFLAGS := $(sort $(EXPORTED_COMPILEFLAGS))
EXPORTED_CFLAGS := $(sort $(EXPORTED_CFLAGS))
EXPORTED_CPPFLAGS := $(sort $(EXPORTED_CPPFLAGS))
EXPORTED_INCLUDES := $(sort $(EXPORTED_INCLUDES))
EXPORTED_ASMFLAGS := $(sort $(EXPORTED_ASMFLAGS))

$(info TOTAL_USER_TARGETS: $(TOTAL_USER_TARGETS))
$(info EXPORTED_OPTFLAGS: $(EXPORTED_OPTFLAGS))
$(info EXPORTED_COMPILEFLAGS: $(EXPORTED_COMPILEFLAGS))
$(info EXPORTED_CFLAGS: $(EXPORTED_CFLAGS))
$(info EXPORTED_CPPFLAGS: $(EXPORTED_CPPFLAGS))
$(info EXPORTED_INCLUDES: $(EXPORTED_INCLUDES))
$(info EXPORTED_ASMFLAGS: $(EXPORTED_ASMFLAGS))
$(info TOTAL_LIBS: $(TOTAL_LIBS))
all:: $(TOTAL_USER_TARGETS)

# GLOBAL_CPPFLAGS comes before GLOBAL_INCLUDES on the compile command-line. This
# is important because we need libcxx's math.h to be picked up before musl's
# when building C++.
GLOBAL_USER_IN_TREE_CPPFLAGS += -I$(RCTEE_SDK_INCLUDE_DIR)/c++/v1
GLOBAL_USER_IN_TREE_COMPILEFLAGS += \
	--sysroot=$(RCTEE_SDK_SYSROOT) \
	-isystem $(RCTEE_SDK_INCLUDE_DIR) \
	-D_LIBCPP_HAS_THREAD_API_PTHREAD \

TA_BASE_LDFLAGS += -L$(RCTEE_SDK_LIB_DIR)

# Restore kernel state
ARCH := $(RCTEE_KERNEL_SAVED_ARCH)
ALLOW_FP_USE := $(RCTEE_KERNEL_SAVED_ALLOW_FP_USE)
SCS_ENABLED := $(RCTEE_KERNEL_SAVED_SCS_ENABLED)
ALLMODULE_OBJS := $(sort $(ALLMODULE_OBJS))

#
# Generate combined user task obj/bin if necessary
#
ifneq ($(strip $(BUNDLED_TA_TASKS)),)
$(info BUNDLED_TA_TASKS: $(BUNDLED_TA_TASKS))
BUNDLED_TA_TASK_ELFS := $(foreach ta, $(BUNDLED_TA_TASKS), $(RCTEE_USER_OUTDIR)/ta/$(notdir $(ta))/$(notdir $(ta)).elf)
BUNDLED_TA_TASK_OBJS := $(patsubst %.elf,%.ko,$(BUNDLED_TA_TASK_ELFS))
BUNDLED_TA_LINK_DIR := kernel/rctee/bundled

$(info BUNDLED_TA_TASK_ELFS: $(BUNDLED_TA_TASK_ELFS))
$(BUNDLED_TA_TASK_OBJS): COMPILER_CC := $(COMPILER_CC)
$(BUNDLED_TA_TASK_OBJS): GLOBAL_COMPILEFLAGS := $(GLOBAL_COMPILEFLAGS)
$(BUNDLED_TA_TASK_OBJS): ARCH_COMPILEFLAGS := $(ARCH_$(ARCH)_COMPILEFLAGS)
$(BUNDLED_TA_TASK_OBJS): BUNDLED_TA_OBJ_ASM:=$(BUNDLED_TA_LINK_DIR)/insert_ta.S
$(BUNDLED_TA_TASK_OBJS): %.ko: %.elf %.manifest $(BUNDLED_TA_OBJ_ASM)
	@$(MKDIR)
	@echo Packing bundled TA as kernel module:  $< to $@
	$(NOECHO)$(COMPILER_CC) -DBUNDLED_TA_ELF=\"$<\" -DMANIFEST_DATA=\"$(word 2,$^)\" $(GLOBAL_COMPILEFLAGS) $(ARCH_COMPILEFLAGS) -c $(BUNDLED_TA_OBJ_ASM) -o $@

EXTRA_OBJS += $(BUNDLED_TA_TASK_OBJS)
EXTRA_LINKER_SCRIPTS += $(BUNDLED_TA_LINK_DIR)/ta_sections.ld
endif

# Reset all task vars
USER_TASK_MODULE :=
DYNAMIC_TA_TASKS :=
TRUSTY_USERSPACE :=
RCTEE_KERNEL_SAVED_ARCH :=
RCTEE_KERNEL_SAVED_ALLOW_FP_USE :=
RCTEE_KERNEL_SAVED_SCS_ENABLED :=
TOTAL_LIBS :=
RCTEE_USER_OUTDIR :=
