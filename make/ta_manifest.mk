#
# Copyright (c) 2020, Google, Inc. All rights reserved
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

# Generate and add constant headers, if necessary
#
# args:
# MODULE : module name (required)
# PY3 : Path of the Python 3 interpreter to use to run the manifest compiler
#       script.
# MODULE_CONSTANTS : JSON files with constants used for both the manifest and C
# 		headers (optional) (CONSTANTS is a deprecated equivalent to
# 		MODULE_CONSTANTS)
# MANIFEST : manifest for the application (optional)
# MANIFEST_OVERLAY : additional overlay manifests for the application (optional)
#
# outputs:
# TRUSTY_APP_MANIFEST_BIN : manifest binary name, if MANIFEST
#
# If neither MOD<PERSON>LE_CONSTANTS nor MANIFEST are set, this file does nothing.

ifneq ($(strip $(MODULE_CONSTANTS)),)
MODULE_INCLUDES += \
	$(COMP_OUTDIR)/constants/include
endif

ifneq ($(strip $(MODULE_CONSTANTS)$(MANIFEST)),)

CONSTANTS_HEADER_DIR := $(COMP_OUTDIR)/constants/include

ifeq ($(strip $(MANIFEST_COMPILER)),)
MANIFEST_COMPILER := build/scripts/manifest_compiler.py
endif

# build manifest objects if we are building an app, otherwise generate shared
# constants headers if constants provided
ifeq ($(call TOBOOL,$(BUILD_TA)),true)

TA_MANIFEST_BIN := $(COMP_OUTDIR)/$(TA_NAME).manifest

# Save the manifest path for use in user-tasks.mk
_MODULES_$(MODULE)_TA_MANIFEST_BIN := $(TA_MANIFEST_BIN)
$(info generating manifest for $(MODULE): $(TA_MANIFEST_BIN))

# TODO Until the SDK supports library variants, this flag will only work as
# intended for applications that have no library dependencies.
$(TA_MANIFEST_BIN): TA_ENABLE_SCS :=
ifeq (false,$(call TOBOOL,$(TA_DISABLE_SCS)))
ifeq (true,$(call TOBOOL,$(SCS_ENABLED)))
$(TA_MANIFEST_BIN): TA_ENABLE_SCS := --enable-shadow-call-stack
endif
endif
ifdef ARCH_$(ARCH)_DEFAULT_USER_SHADOW_STACK_SIZE
$(TA_MANIFEST_BIN): DEFAULT_USER_SHADOW_STACK_SIZE := \
--default-shadow-call-stack-size $(ARCH_$(ARCH)_DEFAULT_USER_SHADOW_STACK_SIZE)
else
$(TA_MANIFEST_BIN): DEFAULT_USER_SHADOW_STACK_SIZE :=
endif
$(TA_MANIFEST_BIN): MANIFEST_COMPILER := $(MANIFEST_COMPILER)
$(TA_MANIFEST_BIN): PY3 := $(PY3)
$(TA_MANIFEST_BIN): CONFIG_CONSTANTS := $(MODULE_CONSTANTS)
$(TA_MANIFEST_BIN): MANIFEST := $(MANIFEST)
$(TA_MANIFEST_BIN): MANIFEST_OVERLAY := $(MANIFEST_OVERLAY)
$(TA_MANIFEST_BIN): HEADER_DIR := $(CONSTANTS_HEADER_DIR)
$(TA_MANIFEST_BIN): $(MANIFEST) $(MANIFEST_OVERLAY) $(MANIFEST_COMPILER) $(MODULE_CONSTANTS)
	@$(MKDIR)
	@echo create TA_MANIFEST_BIN: $< to $@
	$(PY3) $(MANIFEST_COMPILER) $(addprefix -i,$(MANIFEST) $(MANIFEST_OVERLAY)) -o $@ $(addprefix -c,$(CONFIG_CONSTANTS)) --header-dir $(HEADER_DIR) \
	$(TA_ENABLE_SCS) $(DEFAULT_USER_SHADOW_STACK_SIZE)

# We need the constants headers to be generated before the sources are compiled
MODULE_SRCDEPS += $(TA_MANIFEST_BIN)

else # we are not building an app

ifneq ($(strip $(MODULE_CONSTANTS)),)

# generate shared constants headers if only constants and no manifest provided
$(CONSTANTS_HEADER_DIR): MANIFEST_COMPILER := $(MANIFEST_COMPILER)
$(CONSTANTS_HEADER_DIR): PY3 := $(PY3)
$(CONSTANTS_HEADER_DIR): CONFIG_CONSTANTS := $(MODULE_CONSTANTS)
$(CONSTANTS_HEADER_DIR): HEADER_DIR := $(CONSTANTS_HEADER_DIR)
$(CONSTANTS_HEADER_DIR): $(MANIFEST_COMPILER) $(MODULE_CONSTANTS)
	@$(MKDIR)
	@echo create: constants $@ for $(MODULE)
	$(PY3) $(MANIFEST_COMPILER) $(addprefix -c,$(CONFIG_CONSTANTS)) --header-dir $(HEADER_DIR)

MODULE_SRCDEPS += $(CONSTANTS_HEADER_DIR)

endif # MODULE_CONSTANTS is non-empty

endif # TRUSTY_APP = false

endif # MODULE_CONSTANTS and/or MANIFEST is non-empty

CONSTANTS :=
CONSTANTS_HEADER_DIR :=
MODULE_CONSTANTS :=
MANIFEST :=
MANIFEST_OVERLAY :=
