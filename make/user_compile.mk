#We must use prefix COMP to avoid name conflict with open source files
#TODO: should remove COMP_SRCDEPS?
COMP_CSRCS := $(filter %.c,$(COMP_SRCS))
COMP_CPPSRCS := $(filter %.cpp,$(COMP_SRCS))
COMP_CCSRCS := $(filter %.cc,$(COMP_SRCS))
COMP_ASMSRCS := $(filter %.S,$(COMP_SRCS))

COMP_COBJS := $(addprefix $(COMP_OUTDIR)/,$(patsubst %.c,%.o,$(COMP_CSRCS)))
COMP_CPPOBJS := $(addprefix $(COMP_OUTDIR)/,$(patsubst %.cpp,%.o,$(COMP_CPPSRCS)))
COMP_CCOBJS := $(addprefix $(COMP_OUTDIR)/,$(patsubst %.cc,%.o,$(COMP_CCSRCS)))
COMP_ASMOBJS := $(addprefix $(COMP_OUTDIR)/,$(patsubst %.S,%.o,$(COMP_ASMSRCS)))

COMP_OBJS := $(COMP_COBJS) \
               $(COMP_CPPOBJS) \
               $(COMP_CCOBJS) \
               $(COMP_ASMOBJS) \

#add all common flags,TODO: should remove it first
COMP_INCLUDES := $(addprefix -I,$(COMP_INCLUDES))
$(foreach def,$(COMP_DEFINES),\
	$(eval COMP_COMPILEFLAGS += -D$(def)))
COMP_COMPILEFLAGS += -DTRUSTY_USERSPACE=1

#$(info COMP_SRCS = $(COMP_SRCS))
#$(info COMP_OBJS = $(COMP_OBJS))
#$(info COMP_CSRCS = $(COMP_CSRCS))
#$(info COMP_CPPSRCS = $(COMP_CPPSRCS))
#$(info COMP_OPTFLAGS = $(COMP_OPTFLAGS))
#$(info COMP_COMPILEFLAGS = $(COMP_COMPILEFLAGS))
#$(info COMP_CFLAGS = $(COMP_CFLAGS))
#$(info COMP_CPPFLAGS = $(COMP_CPPFLAGS))
#$(info COMP_ASMFLAGS = $(COMP_ASMFLAGS))
#$(info COMP_INCLUDES = $(COMP_INCLUDES))
#$(info EXPORT_CPPFLAGS = $(EXPORT_CPPFLAGS))
#$(info EXPORT_CFLAGS = $(EXPORT_CFLAGS))
#$(info EXPORT_COMPILEFLAGS = $(EXPORT_COMPILEFLAGS))
#$(info EXPORT_OPTFLAGS = $(EXPORT_OPTFLAGS))
#$(info EXPORT_ASMFLAGS = $(EXPORT_ASMFLAGS))

$(COMP_OBJS): COMP_OPTFLAGS:=$(COMP_OPTFLAGS)
$(COMP_OBJS): COMP_COMPILEFLAGS:=$(COMP_COMPILEFLAGS)
$(COMP_OBJS): COMP_CFLAGS:=$(COMP_CFLAGS)
$(COMP_OBJS): COMP_CPPFLAGS:=$(COMP_CPPFLAGS)
$(COMP_OBJS): COMP_ASMFLAGS:=$(COMP_ASMFLAGS)
$(COMP_OBJS): COMP_SRCDEPS:=$(COMP_SRCDEPS)
$(COMP_OBJS): COMP_INCLUDES:=$(COMP_INCLUDES)

$(COMP_COBJS): $(COMP_OUTDIR)/%.o: %.c $(COMP_SRCDEPS)
	@$(MKDIR)
	@echo user compiling c file: $<
	$(NOECHO)$(COMPILER_CC) $(EXPORTED_OPTFLAGS) $(COMP_OPTFLAGS) $(EXPORTED_COMPILEFLAGS) $(COMP_COMPILEFLAGS) $(EXPORTED_CFLAGS) $(COMP_CFLAGS) $(COMP_INCLUDES) $(EXPORTED_INCLUDES) -c $< -MD -MP -MT $@ -MF $(@:%o=%d) -o $@

$(COMP_CPPOBJS): $(COMP_OUTDIR)/%.o: %.cpp $(COMP_SRCDEPS)
	@$(MKDIR)
	@echo user compiling cpp file: $<
	$(NOECHO)$(COMPILER_CC) $(EXPORTED_OPTFLAGS) $(COMP_OPTFLAGS) $(EXPORTED_COMPILEFLAGS) $(COMP_COMPILEFLAGS) $(EXPORTED_CPPFLAGS) $(COMP_CPPFLAGS) $(COMP_INCLUDES) $(EXPORTED_INCLUDES) -c $< -MD -MP -MT $@ -MF $(@:%o=%d) -o $@

$(COMP_CCOBJS): $(COMP_OUTDIR)/%.o: %.cc $(COMP_SRCDEPS)
	@$(MKDIR)
	@echo user compiling cc file: $<
	$(NOECHO)$(COMPILER_CC) $(EXPORTED_OPTFLAGS) $(COMP_OPTFLAGS) $(EXPORTED_COMPILEFLAGS) $(COMP_COMPILEFLAGS) $(EXPORTED_CPPFLAGS) $(COMP_CPPFLAGS) $(COMP_INCLUDES) $(EXPORTED_INCLUDES) -c $< -MD -MP -MT $@ -MF $(@:%o=%d) -o $@

$(COMP_ASMOBJS): $(COMP_OUTDIR)/%.o: %.S $(COMP_SRCDEPS)
	@$(MKDIR)
	@echo user compiling asm file: $<
	$(NOECHO)$(COMPILER_CC) $(EXPORTED_OPTFLAGS) $(COMP_OPTFLAGS) $(EXPORTED_COMPILEFLAGS) $(COMP_COMPILEFLAGS) $(EXPORTED_ASMFLAGS) $(COMP_ASMFLAGS) $(COMP_INCLUDES) $(EXPORTED_INCLUDES) -c $< -MD -MP -MT $@ -MF $(@:%o=%d) -o $@

# clear some variables we set here
COMP_SRCS :=
COMP_CSRCS :=
COMP_CPPSRCS :=
COMP_CCSRCS :=
COMP_ASMSRCS :=
COMP_COBJS :=
COMP_CPPOBJS :=
COMP_CCOBJS :=
COMP_ASMOBJS :=
COMP_OPTFLAGS :=
COMP_COMPILEFLAGS :=
COMP_CFLAGS :=
COMP_CPPFLAGS :=
COMP_ASMFLAGS :=
COMP_SRCDEPS :=
COMP_INCLUDES :=
COMP_DEFINES :=

