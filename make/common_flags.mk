# Remaining flags only apply to the trusty userspace, not the test-runner, which
# is also built with the library system.
ifeq (true,$(call TOBOOL,$(TRUSTY_USERSPACE)))

# Control function inlining
USERSPACE_INLINE_FUNCTIONS ?= true
ifeq ($(call TOBOOL,$(USERSPACE_INLINE_FUNCTIONS)),true)
COMP_COMPILEFLAGS += -finline
else
COMP_COMPILEFLAGS += -fno-inline-functions
endif

# If <PERSON><PERSON> is disabled, don't make PIEs, it burns space
ifneq ($(ASLR), false)
	# Generate PIE code to allow ASLR to be applied
	COMP_COMPILEFLAGS += -fPIC
endif

# LTO
ifneq (true,$(call TOBOOL,$(MODULE_DISABLE_LTO)))
ifeq (true,$(call TOBOOL,$(USER_LTO_ENABLED)))
COMP_COMPILEFLAGS += \
	-fvisibility=hidden \
	-flto=thin \
	-fsplit-lto-unit \

endif

# CFI
MODULE_CFI_ENABLED := false
ifneq (true,$(call TOBOOL,$(MODULE_DISABLE_CFI)))
ifeq (true,$(call TOBOOL,$(CFI_ENABLED)))
MODULE_CFI_ENABLED := true
endif

ifdef USER_CFI_ENABLED
MODULE_CFI_ENABLED := $(call TOBOOL,$(USER_CFI_ENABLED))
endif
endif # !MODULE_DISABLE_CFI

ifeq (true,$(call TOBOOL,$(MODULE_CFI_ENABLED)))
COMP_COMPILEFLAGS += \
	-fsanitize-blacklist=kernel/rctee/lib/ubsan/exemptlist \
	-fsanitize=cfi \
	-DCFI_ENABLED
MODULE_LIBRARY_DEPS += kernel/rctee/lib/ubsan

ifeq (true,$(call TOBOOL,$(CFI_DIAGNOSTICS)))
COMP_COMPILEFLAGS += -fno-sanitize-trap=cfi
endif
endif # MODULE_CFI_ENABLED

endif # !MODULE_DISABLE_LTO

# Branch Target Identification
ifeq (true,$(call TOBOOL,$(ARCH_$(ARCH)_SUPPORTS_BTI)))
ifeq (false,$(call TOBOOL,$(MODULE_DISABLE_BTI)))
COMP_COMPILEFLAGS += -mbranch-protection=bti \
                       -DBTI_ENABLED
endif
endif

# Stack protector
ifneq (true,$(call TOBOOL,$(MODULE_DISABLE_STACK_PROTECTOR)))
ifeq (true,$(call TOBOOL,$(USER_STACK_PROTECTOR)))
COMP_COMPILEFLAGS += -fstack-protector-strong
endif
else
COMP_COMPILEFLAGS += -fno-stack-protector
endif

# Shadow call stack
ifeq (true,$(call TOBOOL,$(SCS_ENABLED)))
# set in arch/$(ARCH)/toolchain.mk iff shadow call stack is supported
ifeq (false,$(call TOBOOL,$(ARCH_$(ARCH)_SUPPORTS_SCS)))
$(error Error: Shadow call stack is not supported for $(ARCH))
endif

ifeq (false,$(call TOBOOL,$(TRUSTY_APP_DISABLE_SCS)))
ifeq (false,$(call TOBOOL,$(MODULE_DISABLE_SCS)))
# architectures that support SCS should set the flag that reserves
# a register for the shadow call stack in their toolchain.mk file
COMP_COMPILEFLAGS += -fsanitize=shadow-call-stack


endif
else  # TRUSTY_APP_DISABLE_SCS
$(warning $(MODULE) has set TRUSTY_APP_DISABLE_SCS, this flag only works as intended for apps w/o dependencies)
endif
endif # SCS_ENABLED

# Code coverage
ifeq (true,$(call TOBOOL,$(USER_COVERAGE_ENABLED)))
ifeq (false,$(call TOBOOL, $(MODULE_DISABLE_COVERAGE)))
LIB_SRC_DEPS += trusty/user/base/lib/sancov

# -fno-optimize-sibling-calls is necessary to get correct caller information in
# the sancov instrumentation.
COMP_COMPILEFLAGS += \
	-fsanitize-coverage-ignorelist=trusty/user/base/lib/sancov/exemptlist \
	-fsanitize-coverage=trace-pc-guard \
	-fno-optimize-sibling-calls

endif
endif

# Source based code coverage
ifeq (true,$(call TOBOOL,$(UNITTEST_COVERAGE_ENABLED)))
ifeq (false,$(call TOBOOL, $(MODULE_DISABLE_COVERAGE)))
COMP_COMPILEFLAGS += -DUNITTEST_COVERAGE  \
	-fprofile-instr-generate \
	-fcoverage-mapping \
	-mllvm \
	-enable-value-profiling=false

endif
endif

# Fuzzing build
ifeq (true,$(call TOBOOL,$(FUZZING_BUILD_ENABLED)))
COMP_COMPILEFLAGS += \
	-DFUZZING_BUILD_MODE_UNSAFE_FOR_PRODUCTION \

endif

# HWASan
ifeq (true,$(call TOBOOL,$(USER_HWASAN_ENABLED)))
MODULE_DEFINES += \
	HWASAN_ENABLED=1 \
	HWASAN_SHADOW_SCALE=4 \

LIB_SRC_DEPS += user/base/lib/hwasan
COMP_COMPILEFLAGS += \
	-fsanitize-blacklist=trusty/user/base/lib/hwasan/exemptlist \
	-fsanitize=hwaddress \
	-mllvm -hwasan-with-tls=0 \
	-mllvm -hwasan-globals=0 \
	-mllvm -hwasan-use-short-granules=0 \

endif

endif # TRUSTY_USERSPACE

MODULE_CFI_ENABLED :=
MODULE_DISABLE_BTI :=
MODULE_DISABLE_CFI :=
MODULE_DISABLE_COVERAGE :=
MODULE_DISABLE_LTO :=
MODULE_DISABLE_SCS :=
MODULE_DISABLE_STACK_PROTECTOR :=
