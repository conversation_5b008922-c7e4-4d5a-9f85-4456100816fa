BUILD_TA := true
include make/compability.mk

#set default params
TA_LDFLAGS ?=
TA_MEMBASE ?=
TA_STRIPFLAGS :=
UNPACKED_TA :=
ENCRYPTED_TA :=
UNSIGNED_TA :=
SIGNED_TA :=

#add some extra flags for TA
TA_LDFLAGS += -z max-page-size=4096 -z separate-loadable-segments --undefined=__aeabi_unwind_cpp_pr0 --gc-sections

# If should strip ELF file
#ifneq ($(TA_SYMTAB_ENABLED), true)
#	TA_STRIPFLAGS := --strip-debug
#else
#	TA_STRIPFLAGS := --strip-unneeded
#endif
# TA should be aligned in 4K
TA_ALIGN_SIZE := 4096
TA_STRIPFLAGS := --strip-all

ifeq (true,$(call TOBOOL,$(UNITTEST_COVERAGE_ENABLED)))
LIB_SRC_DEPS += user/base/lib/line-coverage
TA_STRIPFLAGS += \
    --remove-section=__llvm_covfun \
    --remove-section=__llvm_prf_names \
    --remove-section=__llvm_prf_data \
    --remove-section=__llvm_covmap
endif

# If ASLR is disabled, don't make PIEs, it burns space
ifneq ($(ASLR), false)
    # Generate PIE code to allow ASLR to be applied
    ifeq ($(call TOBOOL,$(TRUSTY_USERSPACE)),true)
        TA_LDFLAGS += -static -pie --no-dynamic-linker -z text -Bsymbolic
    endif
endif

$(info TA_COMP_OUTDIR: $(COMP_OUTDIR))

#add src libs first
TA_LINK_LIBS := $(foreach dep, $(sort $(LIB_SRC_DEPS)), $(notdir $(dep)))
TA_LDFLAGS += $(foreach dep, $(sort $(TA_LINK_LIBS)), $(addprefix -L, $(RCTEE_USER_OUTDIR)/lib/$(dep)))
TA_LINK_LIBS += $(LIB_LINK_DEPS)
TA_LDFLAGS += $(foreach lib, $(sort $(TA_LINK_LIBS)), $(addprefix -l, $(lib)))
$(info TA_LDFLAGS: $(TA_LDFLAGS))

#add dependcy
TA_LINK_BINS := $(foreach dep, $(sort $(LIB_SRC_DEPS)), $(RCTEE_USER_OUTDIR)/lib/$(notdir $(dep))/lib$(notdir $(dep)).a)

# Link app elf
TA_ELF := $(COMP_OUTDIR)/$(TA_NAME).elf
TA_SYMS_ELF := $(COMP_OUTDIR)/$(TA_NAME).syms.elf
$(TA_SYMS_ELF): TA_LINK_BINS := $(TA_LINK_BINS)
$(TA_SYMS_ELF): TA_LDFLAGS := $(TA_LDFLAGS)
$(TA_SYMS_ELF): TA_PACK_OBJS := $(COMP_OBJS)
$(TA_SYMS_ELF): $(COMP_OBJS) $(TA_LINK_BINS)
	@$(MKDIR)
	@echo generate TA_SYMS_ELF: $@
	@echo TA_LD_FIRST_OBJ=$(TA_LD_FIRST_OBJ) TA_LINK_BINS=$(TA_LINK_BINS)
	$(COMPILER_LD) $(TA_LDFLAGS) $(addprefix -Ttext ,$(TA_MEMBASE)) --start-group $(TA_LD_FIRST_OBJ) $(TA_PACK_OBJS) $(TA_LINK_BINS) $(COMPILER_LIBGCC) $(COMPILER_LIBCOV) --end-group -o $@
# And strip ELF if needed
$(info TA_ELF: $(TA_ELF))
$(TA_ELF): TA_STRIPFLAGS := $(TA_STRIPFLAGS)
$(TA_ELF): $(TA_SYMS_ELF)
	@$(MKDIR)
	@echo stripping TA_ELF: $<
	$(NOECHO)$(COMPILER_STRIP) $(TA_STRIPFLAGS) $< -o $@
	@echo page aligning TA: $<
	$(NOECHO)truncate -s %$(TA_ALIGN_SIZE) $@

# build app binary,should remove???
TA_BIN := $(TA_BUILD_OUT)/$(TA_NAME).bin
$(TA_BIN): $(TA_ELF)
	@echo generating TA_BIN: $@
	$(NOECHO)$(COMPILER_OBJCOPY) -O binary $< $@

ifeq (true,$(call TOBOOL,$(USE_DEFAULT_ENC_KEY_$(TA_NAME))))
UNPACKED_TA := $(patsubst %.elf,%.ta.initial,$(TA_ELF))
LOADABLE_APP := $(patsubst %.elf,%.ta,$(TA_ELF))

APP_ENCRYPT_KEY_ID := 0
APP_ENCRYPT_KEY_FILE := $(APPLOADER_ENCRYPT_KEY_$(APP_ENCRYPT_KEY_ID)_FILE)
else
UNPACKED_TA := $(patsubst %.elf,%.ta.initial,$(TA_ELF))
LOADABLE_APP := $(patsubst %.elf,%.ta,$(TA_ELF))
ifneq ($(APPLOADER_ENCRYPT_KEY_ID_FOR_$(TA_NAME)),)
APP_ENCRYPT_KEY_ID := $(APPLOADER_ENCRYPT_KEY_ID_FOR_$(TA_NAME))
APP_ENCRYPT_KEY_FILE := $(APPLOADER_ENCRYPT_KEY_$(APP_ENCRYPT_KEY_ID)_FILE)
endif
endif

$(UNPACKED_TA): $(TA_ELF) $(TA_MANIFEST_BIN) $(TA_PACKAGE_TOOL)
	@$(MKDIR)
	@echo building UNPACKED_TA: $@ from $<
	$(NOECHO)$(TA_PACKAGE_TOOL) -m build $@ $< $(word 2,$^)

ifneq ($(APP_ENCRYPT_KEY_FILE),)
ENCRYPTED_TA := $(patsubst %.elf,%.encrypted,$(TA_ELF))

$(ENCRYPTED_TA): APP_ENCRYPT_KEY_FILE := $(APP_ENCRYPT_KEY_FILE)
$(ENCRYPTED_TA): APP_ENCRYPT_KEY_ID := $(APP_ENCRYPT_KEY_ID)
$(ENCRYPTED_TA): $(UNPACKED_TA) $(APP_ENCRYPT_KEY_FILE) $(TA_PACKAGE_TOOL)
	@$(MKDIR)
	@echo building $@ from $<
	$(NOECHO)$(TA_PACKAGE_TOOL) -m encrypt $@ $< \
		$(APP_ENCRYPT_KEY_FILE) $(APP_ENCRYPT_KEY_ID)

UNSIGNED_TA := $(ENCRYPTED_TA)
else
UNSIGNED_TA := $(UNPACKED_TA)
endif

# If we have an app-specific key identifier then use it,
# otherwise use the global default
ifneq ($(APPLOADER_SIGN_KEY_ID_FOR_$(TA_NAME)),)
APP_SIGN_KEY_ID := $(APPLOADER_SIGN_KEY_ID_FOR_$(TA_NAME))
else
APP_SIGN_KEY_ID := $(APPLOADER_SIGN_KEY_ID)
endif

ifneq ($(APP_SIGN_KEY_ID),)
APP_SIGN_KEY_FILE := $(APPLOADER_SIGN_PRIVATE_KEY_$(APP_SIGN_KEY_ID)_FILE)
endif

ifeq ($(wildcard $(APP_SIGN_KEY_FILE)),)
$(error Dynamic TA $(APP_TOP_MODULE) signing key not found: $(APP_SIGN_KEY_FILE))
endif

SIGNED_TA := $(COMP_OUTDIR)/$(TA_NAME).signed
ifneq ($(APP_SIGN_KEY_FILE),)
$(SIGNED_TA): APP_SIGN_KEY_FILE := $(APP_SIGN_KEY_FILE)
$(SIGNED_TA): APP_SIGN_KEY_ID := $(APP_SIGN_KEY_ID)
$(SIGNED_TA): $(UNSIGNED_TA) $(APP_SIGN_KEY_FILE) $(TA_PACKAGE_TOOL)
	@$(MKDIR)
	@echo building signed TA: $@ from $<
	$(NOECHO)$(TA_PACKAGE_TOOL) -m sign $@ $< \
		$(APP_SIGN_KEY_FILE) $(APP_SIGN_KEY_ID)
else
# If we don't have a signature file, just use the unsigned file as the output
# This is needed because modules that import loadable apps, e.g.,
# app-mgmt-test, need the app files to exist
# Note: apploader will refuse to load the unsigned application
$(SIGNED_TA): $(UNSIGNED_TA)
	@$(MKDIR)
	@echo copying unsigned TA: $< to $@
	@cp $< $@

$(warning Trusted application is not signed: $(LOADABLE_APP))
endif

# Bundled TA & Dynamic TA will both be signed
TOTAL_USER_TARGETS += $(SIGNED_TA)

# Reset local variables
TA_NAME :=
TA_BIN :=
TA_ELF :=
TA_SYMS_ELF :=
TA_ALL_OBJS :=
TA_STRIPFLAGS :=
TA_MANIFEST_BIN :=
TA_DISABLE_SCS :=
TA_LINK_LIBS :=
TA_LINK_BINS :=
LIB_SRC_DEPS :=
LIB_LINK_DEPS :=
UNPACKED_TA :=
UNSIGNED_TA :=
COMP_OBJS :=
TA_PACK_OBJS :=
TA_BUILD_OUT :=
COMP_OUTDIR :=
TA_LDFLAGS :=
TA_MEMBASE :=
BUILD_TA := false

#recurse include deps
ifneq ($(LOCAL_RECURSE_LIBS),)
$(foreach lib, $(LOCAL_RECURSE_LIBS), $(eval $(call include-lib,$(lib))))
endif
LOCAL_RECURSE_LIBS :=