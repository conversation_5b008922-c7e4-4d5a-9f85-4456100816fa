# rename this file to avoid duplicate name with other files ^_^
include make/compability.mk

#now we must get rcrt1&crt1 obj first
ifeq ($(LIB_NAME),libc-main)
TA_LD_FIRST_OBJ := $(COMP_OBJS)
endif

$(info LIB_COMP_OUTDIR: $(COMP_OUTDIR))
LIB_BIN_DEPS := $(foreach dep, $(sort $(LIB_SRC_DEPS)), $(RCTEE_USER_OUTDIR)/lib/$(notdir $(dep))/lib$(notdir $(dep)).a)
LIB_BIN := $(COMP_OUTDIR)/lib$(LIB_NAME).a
$(LIB_BIN): ALL_LIB_OBJS := $(COMP_OBJS)
$(LIB_BIN): $(COMP_OBJS) $(LIB_BIN_DEPS)
	@$(MKDIR)
	@echo creating static lib: $@
	$(NOECHO)rm -f $@
	$(NOECHO)$(AR) rcs $@ $(ALL_LIB_OBJS)

TOTAL_USER_TARGETS += $(LIB_BIN)
$(info LIB_BIN_DEPS: $(LIB_BIN_DEPS))
$(info LIB_BIN: $(LIB_BIN))
$(info COMP_OBJS: $(COMP_OBJS))

# Reset COMP_OBJS for all build tasks
COMP_OBJS :=
COMP_OUTDIR :=
LIB_NAME :=
LIB_BIN :=
LIB_SRC_DEPS :=
LIB_BIN_DEPS :=
LIB_LINK_PATH :=
LIB_LINK_DEPS :=

#recurse include deps
ifneq ($(LOCAL_RECURSE_LIBS),)
$(foreach lib, $(LOCAL_RECURSE_LIBS), $(eval $(call include-lib,$(lib))))
endif
LOCAL_RECURSE_LIBS :=