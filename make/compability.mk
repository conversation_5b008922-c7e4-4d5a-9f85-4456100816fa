#This file is used for compability with LK CONFIGs;
# currently MODULE_SRCDEPS are accpeted only for manifest
#TODO: 1. use new building system, remove this flags since we are RCTEE now
#TODO: 2. remove MODULE_SRCDEPS
COMP_SRCS ?=
COMP_OPTFLAGS ?=
COMP_COMPILEFLAGS ?=
COMP_CFLAGS ?=
COMP_CPPFLAGS ?=
COMP_ASMFLAGS ?=
COMP_INCLUDES ?=
COMP_DEFINES ?=
LIB_SRC_DEPS ?=
COMP_SRCDEPS ?=
COMP_CONSTANTS ?=
EXPORT_INCLUDES ?=
EXPORT_OPTFLAGS ?=
EXPORT_COMPILEFLAGS ?=
EXPORT_CFLAGS ?=
EXPORT_CPPFLAGS ?=
EXPORT_ASMFLAGS ?=
COMP_PATH ?=

ifeq ($(COMP_PATH),)
$(error Compnent PATH must be set!!!)
endif

$(info Parsing Compnent: $(COMP_PATH))

ifeq ($(BUILD_TA),true)
TA_NAME := $(notdir $(COMP_PATH))
COMP_OUTDIR := $(RCTEE_USER_OUTDIR)/ta/$(TA_NAME)
else
LIB_NAME := $(notdir $(COMP_PATH))
COMP_OUTDIR := $(RCTEE_USER_OUTDIR)/lib/$(LIB_NAME)
TOTAL_LIBS += $(COMP_PATH)
endif

ifneq ($(MODULE),)
$(info $(MODULE) is lk build form, just be compatible with it!)

#handle manifest first, TODO: should remove this
ifeq ($(BUILD_TA),true)
include make/ta_manifest.mk
endif

COMP_SRCS :=  $(MODULE_SRCS)
COMP_OPTFLAGS := $(MODULE_OPTFLAGS)
COMP_COMPILEFLAGS := $(MODULE_COMPILEFLAGS)
COMP_CFLAGS := $(MODULE_CFLAGS)
COMP_CPPFLAGS := $(MODULE_CPPFLAGS)
COMP_ASMFLAGS := $(MODULE_ASMFLAGS)
COMP_INCLUDES := $(MODULE_INCLUDES)
COMP_DEFINES := $(MODULE_DEFINES)
LIB_SRC_DEPS := $(MODULE_LIBRARY_DEPS)
EXPORT_INCLUDES := $(MODULE_EXPORT_INCLUDES)
EXPORT_COMPILEFLAGS := $(MODULE_EXPORT_COMPILEFLAGS)
EXPORT_CPPFLAGS := $(MODULE_EXPORT_CPPFLAGS)
EXPORT_CFLAGS := $(MODULE_EXPORT_CFLAGS)
COMP_SRCDEPS := $(MODULE_SRCDEPS)
COMP_CONSTANTS := $(MODULE_CONSTANTS)
EXPORT_OPTFLAGS := $(MODULE_EXPORT_OPTFLAGS)
EXPORT_ASMFLAGS := $(MODULE_EXPORT_ASMFLAGS)
endif

ifeq ($(BUILD_LIB),true)
ifneq ($(LIB_LINK_DEPS),)
$(error lib link deps should not be set, LIB:$(LIB_PATH))
endif
ifeq ($(LIB_NAME),)
$(error lib name must be set, LIB:$(LIB_PATH))
endif
endif

ifneq ($(COMP_OBJS),)
$(error objs should not be set, build task:$(TA_PATH) $(LIB_PATH))
endif

ifeq ($(COMP_SRCS),)
$(warning COMP_SRCS not set:may be a include lib, build task:$(TA_PATH) $(LIB_PATH))
endif

#same rules for TA&LIB
define include-lib
$(eval COMP_PATH := $(1))
$(eval include $(1)/rules.mk)
endef
#same rules for TA&LIB
define add-lib-dependency
$(eval LIB_NAMES := $(subst /,_,$(sort $(1))))
$(foreach name, $(LIB_NAMES), $(eval $(2) += $(RCTEE_TASKS_BUILDDIR)/lib/$(name)/lib$(name).a))
endef

ifeq ($(BUILD_TA),true)
# will let TA choose which allocator,default is dlmalloc
ifeq ($(RCTEE_TA_ALLOCATOR),)
LIB_SRC_DEPS += user/base/lib/dlmalloc
else
LIB_SRC_DEPS += user/base/lib/scudo
endif
#add common depencies, TODO: use SDK libs instead
LIB_SRC_DEPS += user/base/lib/libc-rctee
LIB_SRC_DEPS += user/base/lib/tipc
LIB_SRC_DEPS += user/base/lib/syscall-stubs
LIB_SRC_DEPS += user/base/lib/line-coverage
LIB_SRC_DEPS += user/base/lib/libcxxabi-trusty
LIB_SRC_DEPS += user/base/lib/libstdc++-trusty
LIB_SRC_DEPS += user/base/lib/rng
endif

#add exported flags as global flags
EXPORTED_OPTFLAGS += $(EXPORT_OPTFLAGS)
EXPORTED_COMPILEFLAGS += $(EXPORT_COMPILEFLAGS)
EXPORTED_CFLAGS += $(EXPORT_CFLAGS)
#include should add with -I prefix 
EXPORTED_INCLUDES += $(addprefix -I,$(EXPORT_INCLUDES))
EXPORTED_ASMFLAGS += $(EXPORT_ASMFLAGS)
EXPORTED_CPPFLAGS += $(EXPORT_CPPFLAGS)

include make/common_flags.mk
include make/user_compile.mk


# filter out itself to avoid circular reference
TOTAL_LIBS := $(sort $(TOTAL_LIBS))
LIB_SRC_DEPS := $(filter-out \,$(sort $(LIB_SRC_DEPS)))
LOCAL_RECURSE_LIBS := $(filter-out $(TOTAL_LIBS),$(filter-out $(COMP_PATH),$(LIB_SRC_DEPS)))
TOTAL_LIBS += $(LOCAL_RECURSE_LIBS)

# Reset COMP variables
COMP_PATH :=
EXPORT_OPTFLAGS :=
EXPORT_COMPILEFLAGS :=
EXPORT_CFLAGS :=
EXPORT_INCLUDES :=
EXPORT_CPPFLAGS :=
EXPORT_ASMFLAGS :=

ifneq ($(MODULE),)
#currently reset these variables just for compatiblity,TODO: remove them
$(info $(MODULE) variables will all be reset!)
MODULE :=
MODULE_SRCS :=
MODULE_DEFINES :=
MODULE_OPTFLAGS :=
MODULE_COMPILEFLAGS :=
MODULE_CFLAGS :=
MODULE_CPPFLAGS :=
MODULE_ASMFLAGS :=
MODULE_SRCDEPS :=
MODULE_INCLUDES :=
MODULE_LIBRARY_DEPS :=
MODULE_EXPORT_INCLUDES :=
MODULE_CONSTANTS :=
MODULE_SRCS_FIRST :=
MODULE_EXPORT_COMPILEFLAGS :=
MODULE_EXPORT_CPPFLAGS :=
endif