ifeq ($(<PERSON><PERSON><PERSON>_BINDIR),)
$(error clang bin directory must be set)
endif

ifeq (arm64,$(RCTEE_PLATFORM_ARCH))
STANDARD_ARCH_NAME := aarch64
else
STANDARD_ARCH_NAME := arm
endif


COMPILER_CC := $(CLANG_BINDIR)/clang
COMPILER_LD := $(CLANG_BINDIR)/ld.lld
COMPILER_OBJCOPY := $(CLANG_BINDIR)/llvm-objcopy
COMPILER_STRIP := $(CLANG_BINDIR)/llvm-strip
COMPILER_LIBGCC := $(CLANG_BINDIR)/../runtimes_ndk_cxx/libclang_rt.builtins-$(STANDARD_ARCH_NAME)-android.a

ifeq (true,$(call TOBOOL,$(UNITTEST_COVERAGE_ENABLED)))
COMPILER_LIBCOV := -u__llvm_profile_runtime $(CLANG_BINDIR)/../runtimes_ndk_cxx/libclang_rt.profile-aarch64-android.a
endif

#add some arch configs for our platform
ifeq (arm64,$(RCTEE_PLATFORM_ARCH)) #arm64 begin
ifeq (false,$(call TOBOOL,$(ALLOW_FP_USE)))
EXPORTED_COMPILEFLAGS += -mgeneral-regs-only -DWITH_NO_FP=1
endif

ARCH_arm64_SUPPORTS_BTI := true
ARCH_arm64_SUPPORTS_PAC := true
ARCH_arm64_SUPPORTS_SCS := true
ARCH_arm64_DEFAULT_USER_SHADOW_STACK_SIZE ?= $(ARCH_DEFAULT_SHADOW_STACK_SIZE)
ifeq (true,$(call TOBOOL,$(SCS_ENABLED)))
# architecture-specific flag required for shadow call stack
EXPORTED_COMPILEFLAGS += -ffixed-x18
endif

# PLATFORM_arm64_COMPILEFLAGS allows platform to define additional global
# compile flags that it will be using.
EXPORTED_COMPILEFLAGS += $(PLATFORM_arm64_COMPILEFLAGS)

CLANG_ARM64_TARGET_SYS ?= linux
CLANG_ARM64_TARGET_ABI ?= gnu
EXPORTED_COMPILEFLAGS += -target aarch64-$(CLANG_ARM64_TARGET_SYS)-$(CLANG_ARM64_TARGET_ABI)
else # not arm64
# Arch
ifeq ($(ARM_CPU),armv8-a)
EXPORTED_COMPILEFLAGS += -march=$(ARM_CPU)
else
EXPORTED_COMPILEFLAGS += -mcpu=$(ARM_CPU)
endif

# Floating point support
ifneq ($(ARM_WITHOUT_VFP_NEON),true)
# ARM_WITHOUT_VFP_NEON = false
ifeq (false,$(call TOBOOL,$(ALLOW_FP_USE)))
# This is likely kernel space.
# Don't use neon registers but still support FP ASM.
# The kernel will not save NEON register on interrupt.
EXPORTED_COMPILEFLAGS += -mfpu=vfpv3 -mfloat-abi=softfp -DWITH_NO_FP=1
else # ALLOW_FP_USE = true
# This is likely userspace.
ifeq ($(ARM_CPU),cortex-a7)
EXPORTED_COMPILEFLAGS += -mfpu=neon-vfpv4 -mfloat-abi=softfp
endif
ifeq ($(ARM_CPU),cortex-a15)
EXPORTED_COMPILEFLAGS += -mfpu=neon-vfpv4 -mfloat-abi=softfp
endif
ifeq ($(ARM_CPU),armv8-a)
EXPORTED_COMPILEFLAGS += -mfpu=crypto-neon-fp-armv8 -mfloat-abi=softfp
endif
endif # ALLOW_FP_USE
else # ARM_WITHOUT_VFP_NEON = true
EXPORTED_COMPILEFLAGS += -mfloat-abi=soft
endif # ARM_WITHOUT_VFP_NEON

CLANG_ARM_TARGET_SYS ?= linux
CLANG_ARM_TARGET_ABI ?= gnu

ifeq ($(ENABLE_THUMB),true)
EXPORTED_COMPILEFLAGS += -mthumb -D__thumb__
endif

EXPORTED_COMPILEFLAGS += -target arm-$(CLANG_ARM_TARGET_SYS)-$(CLANG_ARM_TARGET_ABI)
endif #arm64 begin