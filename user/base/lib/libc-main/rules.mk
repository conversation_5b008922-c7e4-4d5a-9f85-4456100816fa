LOCAL_DIR := $(GET_LOCAL_DIR)

MODULE := $(LOCAL_DIR)

# Clang currently generates incorrect code when it simplifies calls to libc
# and then inlines them.  The simplification pass does not set a calling
# convention on the new call, leading to problems when inlining.
# Avoid this bug by disabling LTO for libc.  See: b/161257552
MODULE_DISABLE_LTO := true

MUSL_DIR := opensource_libs/musl

# Internal includes. Should mask public includes - but -isystem guarentees this.
MODULE_INCLUDES += \
	$(MUSL_DIR)/src/internal \
	$(MUSL_DIR)/src/include \

# Musl declares global variables with names like "index" that can conflict with
# function names when _ALL_SOURCE is turned on. Compile Musl as it expects to be
# compiled.
MODULE_COMPILEFLAGS += -U_ALL_SOURCE -D_XOPEN_SOURCE=700

# libc should be freestanding, but the rest of the app should not be.
MODULE_COMPILEFLAGS += -ffreestanding

# Mu<PERSON><PERSON>'s source is not warning clean. Suppress warnings we know about.
MODULE_COMPILEFLAGS += \
	-Wno-parentheses \
	-Wno-sign-compare \
	-Wno-incompatible-pointer-types-discards-qualifiers \
	-Wno-string-plus-int \
	-Wno-missing-braces \
	-Wno-implicit-fallthrough \
	-Wno-unused-but-set-variable \

# Musl is generally not strict about its function prototypes.
# This could be fixed, except for "main". The prototype for main is deliberately
# ill-defined.
MODULE_CFLAGS += -Wno-strict-prototypes

# Musl's math code uses pragma STDC FENV_ACCESS ON.
# Neither Clang nor GCC support this pragma.
# https://wiki.musl-libc.org/mathematical-library.html
MODULE_COMPILEFLAGS += \
	-Wno-unknown-pragmas \
	-Wno-ignored-pragmas \

# Musl will do something like this:
# weak_alias(a, b); weak_alias(b, c);
# But it appears the second statement will get eagerly evaluated to:
# weak_alias(a, c);
# and overriding b will not affect c.  This is likely not intended behavior, but
# it does not matter for us so ignore it.
MODULE_COMPILEFLAGS += \
	-Wno-ignored-attributes \

# The are compares that make sense in 64-bit but do not make sense in 32-bit.
MODULE_COMPILEFLAGS += \
	-Wno-tautological-constant-compare


ifeq ($(ASLR),false)
MODULE_SRCS := $(MUSL_DIR)/crt/crt1.c
else
MODULE_SRCS := $(MUSL_DIR)/crt/rcrt1.c
endif

# TODO extract the early startup code from this module and turn on the stack
# protector for most of libc.
MODULE_DISABLE_STACK_PROTECTOR := true

include make/rctee_lib.mk
