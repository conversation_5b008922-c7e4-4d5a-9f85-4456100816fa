#
# Copyright (c) 2013-2018, Google, Inc. All rights reserved
#
# Permission is hereby granted, free of charge, to any person obtaining
# a copy of this software and associated documentation files
# (the "Software"), to deal in the Software without restriction,
# including without limitation the rights to use, copy, modify, merge,
# publish, distribute, sublicense, and/or sell copies of the Software,
# and to permit persons to whom the Software is furnished to do so,
# subject to the following conditions:
#
# The above copyright notice and this permission notice shall be
# included in all copies or substantial portions of the Software.
#
# THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
# EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF
# MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.
# IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY
# CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT,
# TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE
# SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
#

LOCAL_DIR := $(GET_LOCAL_DIR)
MODULE := $(LOCAL_DIR)

GLOBAL_INCLUDES += \
	$(LOCAL_DIR)/include \

#rctee core
MODULE_SRCS := \
	$(LOCAL_DIR)/rctee_core/event.c \
	$(LOCAL_DIR)/rctee_core/handle.c \
	$(LOCAL_DIR)/rctee_core/handle_set.c \
	$(LOCAL_DIR)/rctee_core/iovec.c \
	$(LOCAL_DIR)/rctee_core/ipc.c \
	$(LOCAL_DIR)/rctee_core/ipc_msg.c \
	$(LOCAL_DIR)/rctee_core/memref.c \
	$(LOCAL_DIR)/rctee_core/syscall.c \
	$(LOCAL_DIR)/rctee_core/uctx.c \
	$(LOCAL_DIR)/rctee_core/uirq.c \
	$(LOCAL_DIR)/rctee_core/util.c \
	$(LOCAL_DIR)/rctee_core/uuid.c \
	$(LOCAL_DIR)/rctee_core/rctee_app.c \

ifeq (true,$(call TOBOOL,$(TEST_BUILD)))
MODULE_SRCS += \
	$(LOCAL_DIR)/rc_app/apploader_mmio_test_apps.c
endif

GLOBAL_DEFINES += \
	WITH_LIB_RCTEE=1 \

# IPC support 
ifeq (true,$(call TOBOOL,$(WITH_RCTEE_IPC)))
GLOBAL_DEFINES += WITH_RCTEE_IPC=1

# check if we want to disable wait_any support
ifeq (false, $(call TOBOOL,$(WITH_NO_WAIT_ANY_SUPPORT)))
GLOBAL_DEFINES += WITH_WAIT_ANY_SUPPORT=1
endif

# by default we want to enable virtio RCIPC device
WITH_RCTEE_VIRTIO_IPC_DEV ?= true
ifeq (true, $(call TOBOOL,$(WITH_RCTEE_VIRTIO_IPC_DEV)))
MODULE_SRCS += \
	$(LOCAL_DIR)/rctee_vitio/vqueue.c \
	$(LOCAL_DIR)/rctee_vitio/smcall.c \
	$(LOCAL_DIR)/rctee_vitio/rctee_virtio.c \
	$(LOCAL_DIR)/rctee_vitio/rcipc_virtio_dev.c \
	$(LOCAL_DIR)/rctee_vitio/rcipc_dev_ql.c

MODULE_DEPS += \
	kernel/rctee/lib/extmem \
	kernel/rctee/lib/sm \

endif

#ipc config
ifneq (true,$(call TOBOOL,$(WITH_CUSTOM_RCTEE_IPC_CONFIG)))
MODULE_SRCS += \
	$(LOCAL_DIR)/rcipc_config.c
endif
endif

#end IPC support  

MODULE_DEPS += \
	kernel/rctee/lib/syscall \
	kernel/rctee/lib/app_manifest \
	kernel/rctee/lib/backtrace \
	kernel/rctee/lib/libc-ext \
	kernel/rctee/lib/rand \
	kernel/rctee/lib/version \

ifneq (,$(wildcard $(LOCAL_DIR)/arch/$(ARCH)/rules.mk))
include $(LOCAL_DIR)/arch/$(ARCH)/rules.mk
endif

GLOBAL_DEFINES += \
	WITH_SYSCALL_TABLE=1 \

include make/module.mk
